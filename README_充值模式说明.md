# Token余额刷新 - 充值模式说明

## 🔍 问题背景

在尝试实现Token余额的"重置"功能时，我们发现了New-API系统的一个重要限制：

**API不支持直接重置 `used_quota`（已使用量）字段**

## 📋 技术原因

通过分析New-API的源代码（`model/token.go`），我们发现：

```go
// Token的Update方法故意排除了used_quota字段
err = DB.Model(token).Select("name", "status", "expired_time", "remain_quota", "unlimited_quota",
        "model_limits_enabled", "model_limits", "allow_ips", "group").Updates(token).Error
```

系统设计理念是：
- `used_quota` 应该通过实际API使用来自动计算
- 不允许手动重置已使用量，以保证数据的准确性

## 🔄 当前行为（充值模式）

### 原本期望的"重置"模式：
```
初始状态: 剩余$50, 已使用$0, 总计$50
使用一天后: 剩余$30, 已使用$20, 总计$50
刷新后: 剩余$50, 已使用$0, 总计$50  ← 这是期望的
```

### 实际的"充值"模式：
```
初始状态: 剩余$50, 已使用$0, 总计$50
使用一天后: 剩余$30, 已使用$20, 总计$50
刷新后: 剩余$50, 已使用$20, 总计$70  ← 这是实际的
```

## 💡 解决方案选择

我们有以下几种选择：

### 方案A：接受充值模式（当前采用）✅
- **优点**：不修改系统代码，安全可靠
- **缺点**：总额度会累积增长
- **适用场景**：将此功能理解为"每日充值"而非"重置"

### 方案B：修改New-API源代码
- **优点**：可以实现真正的重置
- **缺点**：需要修改生产系统，有风险
- **实施**：在Token模型的Update方法中添加used_quota字段

### 方案C：直接操作数据库
- **优点**：可以实现重置
- **缺点**：绕过API，可能导致数据不一致

## 📊 当前实现的改进

我们对代码进行了以下改进：

1. **清晰的日志输出**：
   ```
   📊 当前状态:
      剩余额度: $30.00 (15000000 系统单位)
      已使用额度: $20.00 (10000000 系统单位)  
      总额度: $50.00 (25000000 系统单位)
   📊 目标余额: $55.00 (27500000 系统单位)
   💡 注意: 由于API限制，无法重置已使用量。当前操作为'充值'模式
   ```

2. **移除无效代码**：
   - 移除了尝试设置 `used_quota: 0` 的代码
   - 添加了说明注释

3. **准确的统计信息**：
   - 显示实际的已使用量作为"昨日用量"
   - 清楚说明更新后的总额度

## 🎯 使用建议

### 如果你能接受"充值"模式：
- 继续使用当前版本
- 将此功能理解为"每日充值"
- 定期监控总额度的增长

### 如果你需要真正的"重置"：
- 考虑修改New-API源代码
- 或者使用数据库直接操作（需要额外的安全措施）

## 🔧 技术细节

### 相关文件：
- `model/token.go` - Token模型定义
- `controller/token.go` - Token API控制器
- `router/api-router.go` - API路由配置

### 关键方法：
- `token.Update()` - 更新Token信息（不包含used_quota）
- `increaseTokenQuota()` - 增加剩余额度，减少已使用量
- `decreaseTokenQuota()` - 减少剩余额度，增加已使用量

## 📝 版本历史

- **v1.0** - 初始版本，尝试重置used_quota
- **v1.1** - 发现API限制，调整为充值模式
- **v1.2** - 改进日志输出，添加详细说明

---

*如有疑问或需要进一步的技术支持，请查看git提交历史或联系开发者。*
