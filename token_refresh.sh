#!/bin/bash
# Token余额自动刷新脚本 (Shell版本)
# 每日定时刷新指定Token的余额到设定金额
# 
# 作者: Claude 4.0 Sonnet
# 创建时间: 2025-01-20

set -euo pipefail

# 配置文件路径
CONFIG_FILE="config.json"
LOG_FILE="token_refresh.log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")
            echo -e "${GREEN}[INFO]${NC} $message"
            echo "$timestamp - INFO - $message" >> "$LOG_FILE"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message" >&2
            echo "$timestamp - ERROR - $message" >> "$LOG_FILE"
            ;;
        "WARN")
            echo -e "${YELLOW}[WARN]${NC} $message"
            echo "$timestamp - WARN - $message" >> "$LOG_FILE"
            ;;
        "DEBUG")
            echo -e "${BLUE}[DEBUG]${NC} $message"
            echo "$timestamp - DEBUG - $message" >> "$LOG_FILE"
            ;;
    esac
}

# 检查依赖
check_dependencies() {
    local deps=("curl" "jq")
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            log "ERROR" "缺少依赖: $dep，请先安装"
            exit 1
        fi
    done
}

# 创建示例配置文件
create_example_config() {
    log "INFO" "创建示例配置文件: $CONFIG_FILE"
    
    cat > "$CONFIG_FILE" << 'EOF'
{
  "api_base_url": "https://your-domain.com",
  "access_token": "your_access_token_here",
  "user_id": 123,
  "tokens_to_refresh": [
    {
      "token_id": 1,
      "token_name": "测试Token1",
      "target_quota": 50000,
      "description": "每日刷新到50元"
    },
    {
      "token_id": 2,
      "token_name": "测试Token2",
      "target_quota": 100000,
      "description": "每日刷新到100元"
    }
  ],
  "dry_run": true,
  "timeout": 30
}
EOF
    
    log "INFO" "已创建示例配置文件，请编辑后再运行"
    exit 0
}

# 加载配置
load_config() {
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log "ERROR" "配置文件 $CONFIG_FILE 不存在"
        create_example_config
    fi
    
    # 验证JSON格式
    if ! jq empty "$CONFIG_FILE" 2>/dev/null; then
        log "ERROR" "配置文件格式错误，请检查JSON语法"
        exit 1
    fi
    
    # 读取配置
    API_BASE_URL=$(jq -r '.api_base_url' "$CONFIG_FILE")
    ACCESS_TOKEN=$(jq -r '.access_token' "$CONFIG_FILE")
    USER_ID=$(jq -r '.user_id' "$CONFIG_FILE")
    DRY_RUN=$(jq -r '.dry_run' "$CONFIG_FILE")
    TIMEOUT=$(jq -r '.timeout // 30' "$CONFIG_FILE")
    
    # 验证必要字段
    if [[ "$API_BASE_URL" == "null" || "$API_BASE_URL" == "your-domain.com" ]]; then
        log "ERROR" "请在配置文件中设置正确的 api_base_url"
        exit 1
    fi
    
    if [[ "$ACCESS_TOKEN" == "null" || "$ACCESS_TOKEN" == "your_access_token_here" ]]; then
        log "ERROR" "请在配置文件中设置正确的 access_token"
        exit 1
    fi
    
    if [[ "$USER_ID" == "null" || "$USER_ID" == "123" ]]; then
        log "ERROR" "请在配置文件中设置正确的 user_id"
        exit 1
    fi
    
    log "INFO" "配置文件加载成功"
}

# 获取Token信息
get_token_info() {
    local token_id=$1
    local url="${API_BASE_URL}/api/token/${token_id}"
    
    local response
    response=$(curl -s \
        --max-time "$TIMEOUT" \
        -H "Content-Type: application/json" \
        -H "Authorization: $ACCESS_TOKEN" \
        -H "New-Api-User: $USER_ID" \
        "$url" 2>/dev/null) || {
        log "ERROR" "请求Token $token_id 信息时网络错误"
        return 1
    }
    
    # 检查响应是否为有效JSON
    if ! echo "$response" | jq empty 2>/dev/null; then
        log "ERROR" "Token $token_id 返回无效响应"
        return 1
    fi
    
    # 检查API调用是否成功
    local success
    success=$(echo "$response" | jq -r '.success // false')
    if [[ "$success" != "true" ]]; then
        local message
        message=$(echo "$response" | jq -r '.message // "未知错误"')
        log "ERROR" "获取Token $token_id 信息失败: $message"
        return 1
    fi
    
    echo "$response"
}

# 更新Token余额
update_token_quota() {
    local token_info=$1
    local target_quota=$2
    local url="${API_BASE_URL}/api/token/"
    
    # 构建更新数据
    # 注意：由于API限制，used_quota字段无法通过Update接口重置
    # 当前操作为"充值"模式，只更新剩余额度
    local update_data
    update_data=$(echo "$token_info" | jq --argjson quota "$target_quota" '
        .data | {
            id: .id,
            name: .name,
            expired_time: .expired_time,
            remain_quota: $quota,
            unlimited_quota: .unlimited_quota,
            model_limits_enabled: .model_limits_enabled,
            model_limits: .model_limits,
            allow_ips: .allow_ips,
            group: .group
        }
    ')
    
    local response
    response=$(curl -s \
        --max-time "$TIMEOUT" \
        -X PUT \
        -H "Content-Type: application/json" \
        -H "Authorization: $ACCESS_TOKEN" \
        -H "New-Api-User: $USER_ID" \
        -d "$update_data" \
        "$url" 2>/dev/null) || {
        log "ERROR" "更新Token余额时网络错误"
        return 1
    }
    
    # 检查响应
    if ! echo "$response" | jq empty 2>/dev/null; then
        log "ERROR" "更新Token余额返回无效响应"
        return 1
    fi
    
    local success
    success=$(echo "$response" | jq -r '.success // false')
    if [[ "$success" != "true" ]]; then
        local message
        message=$(echo "$response" | jq -r '.message // "未知错误"')
        log "ERROR" "更新Token余额失败: $message"
        return 1
    fi
    
    return 0
}

# 刷新单个Token
refresh_single_token() {
    local token_config=$1
    local token_id token_name target_quota
    
    token_id=$(echo "$token_config" | jq -r '.token_id')
    token_name=$(echo "$token_config" | jq -r '.token_name // ("Token-" + (.token_id | tostring))')
    target_quota=$(echo "$token_config" | jq -r '.target_quota')
    
    log "INFO" "🔄 开始处理 $token_name (ID: $token_id)"
    
    # 获取当前Token信息
    local token_info
    if ! token_info=$(get_token_info "$token_id"); then
        log "ERROR" "❌ 无法获取Token $token_id 的信息，跳过"
        return 1
    fi
    
    local current_quota
    current_quota=$(echo "$token_info" | jq -r '.data.remain_quota // 0')
    
    log "INFO" "📊 当前余额: $current_quota, 目标余额: $target_quota"
    
    # 检查是否需要更新
    if [[ "$current_quota" == "$target_quota" ]]; then
        log "INFO" "✨ Token $token_name 余额已经是目标值，无需更新"
        return 0
    fi
    
    # 干运行模式
    if [[ "$DRY_RUN" == "true" ]]; then
        log "INFO" "🧪 [干运行模式] 将会把 $token_name 余额从 $current_quota 更新为 $target_quota"
        return 0
    fi
    
    # 执行更新
    if update_token_quota "$token_info" "$target_quota"; then
        log "INFO" "✅ Token $token_name (ID: $token_id) 余额已更新为 $target_quota"
        return 0
    else
        log "ERROR" "❌ 更新Token $token_name (ID: $token_id) 余额失败"
        return 1
    fi
}

# 主函数
main() {
    log "INFO" "🚀 开始执行Token余额刷新任务"
    log "INFO" "⏰ 执行时间: $(date '+%Y-%m-%d %H:%M:%S')"
    
    # 检查依赖
    check_dependencies
    
    # 加载配置
    load_config
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log "INFO" "🧪 当前为干运行模式，不会实际更新数据"
    fi
    
    # 获取Token列表
    local tokens_count
    tokens_count=$(jq '.tokens_to_refresh | length' "$CONFIG_FILE")
    log "INFO" "📝 共需要处理 $tokens_count 个Token"
    
    local success_count=0
    local failed_count=0
    
    # 处理每个Token
    for ((i=0; i<tokens_count; i++)); do
        local token_config
        token_config=$(jq ".tokens_to_refresh[$i]" "$CONFIG_FILE")
        
        log "INFO" "📍 处理进度: $((i+1))/$tokens_count"
        
        if refresh_single_token "$token_config"; then
            ((success_count++))
        else
            ((failed_count++))
        fi
    done
    
    # 输出总结
    log "INFO" "=================================================="
    log "INFO" "📊 刷新任务完成统计:"
    log "INFO" "✅ 成功: $success_count 个"
    log "INFO" "❌ 失败: $failed_count 个"
    log "INFO" "📝 总计: $tokens_count 个"
    log "INFO" "=================================================="
    
    # 根据结果设置退出码
    if [[ $failed_count -gt 0 ]]; then
        exit 1
    else
        exit 0
    fi
}

# 信号处理
trap 'log "ERROR" "❌ 用户中断执行"; exit 130' INT TERM

# 执行主函数
main "$@"
