#!/usr/bin/env python3
import json
import requests

# 从配置文件读取设置
with open('config.json', 'r', encoding='utf-8') as f:
    config = json.load(f)

# 设置请求头
headers = {
    'Content-Type': 'application/json',
    'Authorization': config['access_token'],
    'New-Api-User': str(config['user_id'])
}

# 获取Token信息
token_id = config['tokens_to_refresh'][0]['token_id']
url = f"{config['api_base_url']}/api/token/{token_id}"

print(f"🔍 获取Token {token_id} 的详细信息...")
response = requests.get(url, headers=headers, timeout=30)

if response.status_code == 200:
    data = response.json()
    if data.get('success'):
        token_info = data.get('data', {})
        print("📊 当前Token信息:")
        print(f"   ID: {token_info.get('id')}")
        print(f"   名称: {token_info.get('name')}")
        print(f"   剩余额度: {token_info.get('remain_quota', 0)}")
        print(f"   已使用额度: {token_info.get('used_quota', 0)}")
        print(f"   总额度: {token_info.get('remain_quota', 0) + token_info.get('used_quota', 0)}")
        
        # 尝试不同的API端点和方法
        print("\n🧪 测试1: 尝试重置使用量API...")
        reset_url = f"{config['api_base_url']}/api/token/{token_id}/reset"
        reset_response = requests.post(reset_url, headers=headers, timeout=30)
        print(f"   重置API响应状态: {reset_response.status_code}")
        print(f"   重置API响应内容: {reset_response.text}")

        print("\n🧪 测试2: 尝试清空使用量API...")
        clear_url = f"{config['api_base_url']}/api/token/{token_id}/clear"
        clear_response = requests.post(clear_url, headers=headers, timeout=30)
        print(f"   清空API响应状态: {clear_response.status_code}")
        print(f"   清空API响应内容: {clear_response.text}")

        print("\n🧪 测试3: 尝试只设置remain_quota，不包含used_quota...")
        update_url = f"{config['api_base_url']}/api/token/"

        update_data_without_used = {
            "id": token_info['id'],
            "name": token_info['name'],
            "expired_time": token_info['expired_time'],
            "remain_quota": 27500000,  # $55
            # 不包含used_quota字段
            "unlimited_quota": token_info['unlimited_quota'],
            "model_limits_enabled": token_info['model_limits_enabled'],
            "model_limits": token_info['model_limits'],
            "allow_ips": token_info['allow_ips'],
            "group": token_info['group']
        }

        print("📤 发送不含used_quota的更新请求...")
        update_response_2 = requests.put(update_url, json=update_data_without_used, headers=headers, timeout=30)
        print(f"   响应状态: {update_response_2.status_code}")
        print(f"   响应内容: {update_response_2.text}")

        print("\n🧪 测试4: 尝试包含used_quota的更新...")
        update_data = {
            "id": token_info['id'],
            "name": token_info['name'],
            "expired_time": token_info['expired_time'],
            "remain_quota": 27500000,  # $55
            "used_quota": 0,  # 重置为0
            "unlimited_quota": token_info['unlimited_quota'],
            "model_limits_enabled": token_info['model_limits_enabled'],
            "model_limits": token_info['model_limits'],
            "allow_ips": token_info['allow_ips'],
            "group": token_info['group']
        }
        
        print("📤 发送更新请求...")
        print(f"   更新数据: {json.dumps(update_data, indent=2)}")
        
        update_response = requests.put(update_url, json=update_data, headers=headers, timeout=30)
        print(f"   响应状态: {update_response.status_code}")
        print(f"   响应内容: {update_response.text}")
        
        if update_response.status_code == 200:
            update_result = update_response.json()
            if update_result.get('success'):
                print("✅ 更新成功")
                
                # 再次获取Token信息查看结果
                print("\n🔍 更新后再次获取Token信息...")
                check_response = requests.get(url, headers=headers, timeout=30)
                if check_response.status_code == 200:
                    check_data = check_response.json()
                    if check_data.get('success'):
                        new_token_info = check_data.get('data', {})
                        print("📊 更新后Token信息:")
                        print(f"   剩余额度: {new_token_info.get('remain_quota', 0)}")
                        print(f"   已使用额度: {new_token_info.get('used_quota', 0)}")
                        print(f"   总额度: {new_token_info.get('remain_quota', 0) + new_token_info.get('used_quota', 0)}")
            else:
                print(f"❌ 更新失败: {update_result.get('message', '未知错误')}")
        else:
            print(f"❌ 更新请求失败: {update_response.status_code}")
    else:
        print(f"❌ 获取Token信息失败: {data.get('message', '未知错误')}")
else:
    print(f"❌ 请求失败: {response.status_code}")
